#!/usr/bin/env python3
"""
MHTML Exam Results Analyzer
Analyzes SSC Online Examination results from MHTML file
"""

import re
from pathlib import Path

def analyze_mhtml_exam_results(file_path):
    """
    Analyze exam results from MHTML file
    
    Color coding:
    - Green: Correct option selected (answered correctly)
    - Red: Wrong option selected (answered incorrectly) 
    - Yellow: Correct option (but not selected by candidate)
    - Gray: Not answered
    """
    
    try:
        # Try different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        content = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    content = file.read()
                print(f"Successfully read file with {encoding} encoding")
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            print("Error: Could not read file with any supported encoding")
            return None

    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        return None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None
    
    # Count total questions by finding Q.No: patterns
    question_pattern = r'Q\.No:&nbsp;(\d+)'
    questions = re.findall(question_pattern, content)
    total_questions = len(questions)
    
    # Count different answer types by background colors
    green_pattern = r'bgcolor="green"'  # Correct answers
    red_pattern = r'bgcolor="red"'      # Wrong answers  
    yellow_pattern = r'bgcolor="yellow"' # Correct option (not selected)
    gray_pattern = r'Not Answered'      # Unanswered questions
    
    correct_answers = len(re.findall(green_pattern, content))
    wrong_answers = len(re.findall(red_pattern, content))
    yellow_options = len(re.findall(yellow_pattern, content))
    not_answered = len(re.findall(gray_pattern, content)) - 1  # Subtract 1 for legend
    
    # Calculate attempted questions
    attempted_questions = correct_answers + wrong_answers
    
    # Calculate accuracy
    accuracy = (correct_answers / attempted_questions * 100) if attempted_questions > 0 else 0
    
    # Verify totals
    total_check = correct_answers + wrong_answers + not_answered
    
    results = {
        'total_questions': total_questions,
        'attempted_questions': attempted_questions,
        'correct_answers': correct_answers,
        'wrong_answers': wrong_answers,
        'not_answered': not_answered,
        'accuracy_percentage': round(accuracy, 2),
        'yellow_options': yellow_options,  # These are correct options that weren't selected
        'total_check': total_check
    }
    
    return results

def print_detailed_analysis(results):
    """Print detailed analysis of exam results"""
    if not results:
        return
    
    print("=" * 60)
    print("SSC ONLINE EXAMINATION - DETAILED ANALYSIS")
    print("=" * 60)
    
    print(f"\n📊 OVERALL STATISTICS:")
    print(f"   Total Questions: {results['total_questions']}")
    print(f"   Questions Attempted: {results['attempted_questions']}")
    print(f"   Questions Not Answered: {results['not_answered']}")
    
    print(f"\n✅ PERFORMANCE BREAKDOWN:")
    print(f"   Correct Answers: {results['correct_answers']} (Green background)")
    print(f"   Wrong Answers: {results['wrong_answers']} (Red background)")
    print(f"   Accuracy: {results['accuracy_percentage']}%")
    
    print(f"\n📈 ADDITIONAL INSIGHTS:")
    print(f"   Attempt Rate: {(results['attempted_questions']/results['total_questions']*100):.1f}%")
    print(f"   Questions Left Blank: {results['not_answered']}")
    print(f"   Yellow Options Found: {results['yellow_options']} (Correct options not selected)")
    
    print(f"\n🔍 VERIFICATION:")
    print(f"   Total Check: {results['total_check']} (should equal {results['total_questions']})")
    if results['total_check'] == results['total_questions']:
        print("   ✓ Analysis verified - all questions accounted for")
    else:
        print("   ⚠ Warning: Total doesn't match - please verify manually")
    
    print("\n" + "=" * 60)

def main():
    """Main function to run the analysis"""
    file_path = "harsh.mhtml"
    
    print("Analyzing MHTML exam results...")
    results = analyze_mhtml_exam_results(file_path)
    
    if results:
        print_detailed_analysis(results)
        
        # Quick summary
        print(f"\n🎯 QUICK SUMMARY:")
        print(f"   Out of {results['total_questions']} questions:")
        print(f"   • {results['attempted_questions']} attempted")
        print(f"   • {results['correct_answers']} answered correctly")
        print(f"   • {results['wrong_answers']} answered incorrectly") 
        print(f"   • {results['not_answered']} left unanswered")
        print(f"   • Overall accuracy: {results['accuracy_percentage']}%")
    else:
        print("Failed to analyze the file.")

if __name__ == "__main__":
    main()
